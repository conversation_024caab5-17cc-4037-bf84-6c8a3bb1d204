// Authentication and Authorization System

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.users = this.loadUsers();
        this.init();
    }

    init() {
        // Check if user is logged in
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
        }

        // Initialize demo users if none exist
        if (this.users.length === 0) {
            this.initializeDemoUsers();
        }
    }

    initializeDemoUsers() {
        const demoUsers = [
            {
                id: 1,
                email: '<EMAIL>',
                password: 'admin123',
                firstName: 'Admin',
                lastName: 'User',
                role: 'admin',
                status: 'active',
                avatar: null,
                phone: '+****************',
                address: '123 Admin Street, City, State 12345',
                joinDate: '2024-01-01',
                lastLogin: new Date().toISOString(),
                preferences: {
                    notifications: true,
                    newsletter: true,
                    darkMode: false
                },
                stats: {
                    totalOrders: 0,
                    totalSpent: 0,
                    favoriteItems: 0
                }
            },
            {
                id: 2,
                email: '<EMAIL>',
                password: 'user123',
                firstName: '<PERSON>',
                lastName: 'Doe',
                role: 'user',
                status: 'active',
                avatar: null,
                phone: '+****************',
                address: '456 User Avenue, City, State 67890',
                joinDate: '2024-02-15',
                lastLogin: new Date().toISOString(),
                preferences: {
                    notifications: true,
                    newsletter: false,
                    darkMode: true
                },
                stats: {
                    totalOrders: 12,
                    totalSpent: 1250.99,
                    favoriteItems: 8
                }
            },
            {
                id: 3,
                email: '<EMAIL>',
                password: 'user123',
                firstName: 'Jane',
                lastName: 'Smith',
                role: 'user',
                status: 'active',
                avatar: null,
                phone: '+****************',
                address: '789 Customer Lane, City, State 54321',
                joinDate: '2024-03-10',
                lastLogin: new Date(Date.now() - 86400000).toISOString(), // Yesterday
                preferences: {
                    notifications: false,
                    newsletter: true,
                    darkMode: false
                },
                stats: {
                    totalOrders: 5,
                    totalSpent: 599.50,
                    favoriteItems: 15
                }
            },
            {
                id: 4,
                email: '<EMAIL>',
                password: 'vip123',
                firstName: 'VIP',
                lastName: 'User',
                role: 'vip',
                status: 'active',
                avatar: null,
                phone: '+****************',
                address: '999 VIP Boulevard, City, State 11111',
                joinDate: '2024-01-15',
                lastLogin: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                preferences: {
                    notifications: true,
                    newsletter: true,
                    darkMode: true
                },
                stats: {
                    totalOrders: 25,
                    totalSpent: 2500.00,
                    favoriteItems: 30
                }
            }
        ];

        this.users = demoUsers;
        this.saveUsers();
    }

    loadUsers() {
        const users = localStorage.getItem('theprojectfaith_users');
        return users ? JSON.parse(users) : [];
    }

    saveUsers() {
        localStorage.setItem('theprojectfaith_users', JSON.stringify(this.users));
    }

    async login(email, password) {
        const user = this.users.find(u => u.email === email && u.password === password);
        
        if (user) {
            if (user.status === 'suspended') {
                throw new Error('Account suspended. Please contact support.');
            }
            
            // Update last login
            user.lastLogin = new Date().toISOString();
            this.saveUsers();
            
            // Set current user
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            
            return user;
        } else {
            throw new Error('Invalid email or password');
        }
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        window.location.href = 'login.html';
    }

    isLoggedIn() {
        return this.currentUser !== null;
    }

    isAdmin() {
        return this.currentUser && this.currentUser.role === 'admin';
    }

    isVIP() {
        return this.currentUser && (this.currentUser.role === 'vip' || this.currentUser.role === 'admin');
    }

    requireAuth() {
        if (!this.isLoggedIn()) {
            window.location.href = 'login.html';
            return false;
        }
        return true;
    }

    requireAdmin() {
        if (!this.requireAuth()) return false;

        if (!this.isAdmin()) {
            alert('Access denied. Admin privileges required.');
            window.location.href = 'index.html';
            return false;
        }
        return true;
    }

    requireVIP() {
        if (!this.isLoggedIn()) {
            return false; // Not logged in
        }

        if (!this.isVIP()) {
            return false; // Not VIP
        }
        return true;
    }

    getAccessLevel() {
        if (!this.isLoggedIn()) {
            return 'guest';
        }

        if (this.isAdmin()) {
            return 'admin';
        }

        if (this.isVIP()) {
            return 'vip';
        }

        return 'user';
    }

    getCurrentUser() {
        return this.currentUser;
    }

    updateUser(userId, updates) {
        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
            this.users[userIndex] = { ...this.users[userIndex], ...updates };
            this.saveUsers();
            
            // Update current user if it's the same user
            if (this.currentUser && this.currentUser.id === userId) {
                this.currentUser = this.users[userIndex];
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }
            
            return this.users[userIndex];
        }
        return null;
    }

    getAllUsers() {
        return this.users;
    }

    getUserById(id) {
        return this.users.find(u => u.id === id);
    }



    async signup(userData) {
        const { firstName, lastName, email, password, newsletter = false, role = 'user' } = userData;

        console.log('AuthManager.signup called with:', { firstName, lastName, email, role }); // Debug log

        // Validate required fields
        if (!firstName || !lastName || !email || !password) {
            throw new Error('All fields are required');
        }

        // Check if email already exists
        const existingUser = this.users.find(u => u.email.toLowerCase() === email.toLowerCase());
        if (existingUser) {
            throw new Error('An account with this email already exists');
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Please enter a valid email address');
        }

        // Validate password strength
        if (password.length < 6) { // Reduced from 8 to 6 for easier testing
            throw new Error('Password must be at least 6 characters long');
        }

        // Create new user directly
        const newUser = {
            id: Math.max(...this.users.map(u => u.id), 0) + 1,
            firstName: firstName.trim(),
            lastName: lastName.trim(),
            email: email.toLowerCase().trim(),
            password: password,
            role: role, // Allow custom role
            avatar: null,
            phone: '',
            address: '',
            joinDate: new Date().toISOString(),
            lastLogin: null,
            status: 'active',
            preferences: {
                notifications: true,
                newsletter: newsletter,
                darkMode: false
            },
            stats: {
                totalOrders: 0,
                totalSpent: 0,
                favoriteItems: 0
            }
        };

        console.log('Creating new user:', newUser); // Debug log

        this.users.push(newUser);
        this.saveUsers();

        console.log('User saved. Total users now:', this.users.length); // Debug log

        // Automatically log in the new user
        this.currentUser = newUser;
        localStorage.setItem('currentUser', JSON.stringify(newUser));

        return newUser;
    }

    // Method to create account with specific role (for admin use)
    createAccountWithRole(userData) {
        return this.signup(userData);
    }

    deleteUser(userId) {
        const userIndex = this.users.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
            this.users.splice(userIndex, 1);
            this.saveUsers();
            return true;
        }
        return false;
    }

    suspendUser(userId) {
        return this.updateUser(userId, { status: 'suspended' });
    }

    activateUser(userId) {
        return this.updateUser(userId, { status: 'active' });
    }

    changePassword(userId, currentPassword, newPassword) {
        const user = this.getUserById(userId);
        if (!user) {
            throw new Error('User not found');
        }

        if (user.password !== currentPassword) {
            throw new Error('Current password is incorrect');
        }

        return this.updateUser(userId, { password: newPassword });
    }

    // Search and filter users
    searchUsers(query) {
        const searchTerm = query.toLowerCase();
        return this.users.filter(user => 
            user.firstName.toLowerCase().includes(searchTerm) ||
            user.lastName.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm)
        );
    }

    filterUsers(filters) {
        let filteredUsers = [...this.users];

        if (filters.status) {
            filteredUsers = filteredUsers.filter(user => user.status === filters.status);
        }

        if (filters.role) {
            filteredUsers = filteredUsers.filter(user => user.role === filters.role);
        }

        if (filters.dateFrom) {
            filteredUsers = filteredUsers.filter(user => 
                new Date(user.joinDate) >= new Date(filters.dateFrom)
            );
        }

        if (filters.dateTo) {
            filteredUsers = filteredUsers.filter(user => 
                new Date(user.joinDate) <= new Date(filters.dateTo)
            );
        }

        return filteredUsers;
    }

    // Get user statistics
    getUserStats() {
        const total = this.users.length;
        const active = this.users.filter(u => u.status === 'active').length;
        const suspended = this.users.filter(u => u.status === 'suspended').length;
        const admins = this.users.filter(u => u.role === 'admin').length;
        const newThisMonth = this.users.filter(u => {
            const joinDate = new Date(u.joinDate);
            const now = new Date();
            return joinDate.getMonth() === now.getMonth() && 
                   joinDate.getFullYear() === now.getFullYear();
        }).length;

        return {
            total,
            active,
            suspended,
            admins,
            newThisMonth,
            activePercentage: total > 0 ? Math.round((active / total) * 100) : 0
        };
    }
}

// Initialize global auth manager
window.authManager = new AuthManager();

// Utility functions for UI
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getInitials(firstName, lastName) {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
}

function getStatusBadgeClass(status) {
    const statusClasses = {
        'active': 'status-active',
        'inactive': 'status-inactive',
        'suspended': 'status-suspended',
        'pending': 'status-pending'
    };
    return statusClasses[status] || 'status-inactive';
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthManager, formatDate, formatDateTime, getInitials, getStatusBadgeClass };
}
