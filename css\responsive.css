/* Mobile First Responsive Design */

/* Extra Small Devices (phones, 320px and up) */
@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .navbar {
        top: 0;
        width: 100%;
        border-radius: 0;
        padding: 0.5rem 0;
    }

    .nav-container {
        padding: 0 10px;
    }

    .nav-center {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        z-index: 999;
        margin-left: 0;
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
    }

    .nav-center.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        flex-direction: column;
        padding: 0;
        gap: 0;
        order: 2;
        width: 100%;
    }
    
    .nav-links li {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    
    .nav-links a {
        display: block;
        padding: 1rem;
    }
    
    .search-box {
        order: 1;
        width: 100%;
        margin: 0;
        padding: 10px 16px;
    }

    .search-box input {
        width: 100%;
    }

    .nav-icons {
        gap: 0.5rem;
    }

    .nav-icon {
        padding: 8px;
    }

    .mobile-menu-btn {
        display: block;
        padding: 8px;
        font-size: 1.2rem;
    }

    .logo svg {
        width: 70px;
        height: 30px;
    }

    .hero {
        min-height: 100vh;
        height: auto;
        padding-top: 80px;
        width: 100%;
    }

    .hero-content h1 {
        font-size: 2.2rem;
        line-height: 1.1;
        margin-bottom: 1rem;
    }

    .hero-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-slide {
        gap: 2rem;
        padding: 1rem 0;
        min-height: 60vh;
        width: 100%;
    }

    .hero-image img {
        height: 280px;
        max-height: 40vh;
        width: 100%;
    }

    .hero-stats {
        flex-direction: row;
        justify-content: space-around;
        gap: 1rem;
    }

    .stat-item {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .hero-badge {
        font-size: 0.8rem;
        padding: 6px 16px;
        margin-bottom: 1.5rem;
    }

    .floating-card {
        display: none; /* Hide on very small screens */
    }
    
    .section-title {
        font-size: 1.8rem;
    }

    .chroma-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .chroma-card {
        height: 200px;
        transform-style: initial;
    }

    .chroma-card:hover {
        transform: translateY(-3px);
    }

    .chroma-content {
        padding: 0.8rem;
    }

    .chroma-icon {
        font-size: 1.3rem;
        margin-bottom: 0.4rem;
    }

    .chroma-content h3 {
        font-size: 0.9rem;
        margin-bottom: 0.2rem;
    }

    .chroma-content p {
        font-size: 0.7rem;
        margin-bottom: 0.4rem;
    }

    .chroma-link {
        padding: 0.3rem 0.8rem;
        font-size: 0.7rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1rem;
        width: 100%;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    #cartModal .modal-content {
        width: 95%;
        max-width: none;
        margin: 1rem;
        min-width: 280px;
        max-height: 90vh;
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
    }

    .cart-item-image {
        width: 80px;
        height: 80px;
        margin: 0 auto;
    }

    .cart-item-actions {
        justify-content: center;
    }

    .quantity-controls {
        justify-content: center;
    }
}

/* Small Devices (landscape phones, 576px and up) */
@media (max-width: 768px) {
    .navbar {
        top: 0;
        width: 100%;
        border-radius: 0;
        padding: 0.8rem 0;
    }

    .nav-container {
        padding: 0 15px;
    }

    .nav-center {
        position: fixed;
        top: 70px;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        z-index: 999;
        margin-left: 0;
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
    }

    .nav-center.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        flex-direction: column;
        padding: 0;
        gap: 0;
        order: 2;
        width: 100%;
    }
    
    .nav-links li {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid #eee;
    }
    
    .nav-links a {
        display: block;
        padding: 1rem;
    }
    
    .search-box {
        order: 1;
        width: 100%;
        margin: 0;
        padding: 10px 16px;
    }

    .search-box input {
        width: 100%;
    }

    .nav-icons {
        gap: 0.8rem;
    }

    .nav-icon {
        padding: 8px;
    }

    .mobile-menu-btn {
        display: block;
        padding: 8px;
        font-size: 1.3rem;
    }

    .logo svg {
        width: 80px;
        height: 35px;
    }

    .hero {
        min-height: 100vh;
        height: auto;
        padding-top: 100px;
        width: 100%;
    }

    .hero-slide {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
        padding: 2rem 0;
        min-height: 70vh;
        width: 100%;
    }

    .hero-content h1 {
        font-size: 2.8rem;
        line-height: 1.2;
    }

    .hero-content p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .hero-image {
        order: -1;
    }

    .hero-image img {
        height: 350px;
        max-height: 50vh;
        border-radius: 20px;
        width: 100%;
    }

    .hero-cta-group {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .cta-btn {
        width: 100%;
        max-width: 320px;
        min-width: 200px;
        padding: 16px 32px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
        align-items: center;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
        margin-bottom: 0;
    }

    .floating-card {
        padding: 10px 15px;
        font-size: 0.8rem;
    }

    .scroll-indicator {
        bottom: 20px;
    }
    
    .chroma-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        width: 100%;
    }

    .chroma-card {
        height: 220px;
        min-height: 200px;
        width: 100%;
    }

    .chroma-content {
        padding: 1.2rem;
    }

    .chroma-icon {
        font-size: 1.6rem;
        margin-bottom: 0.5rem;
    }

    .chroma-content h3 {
        font-size: 1.1rem;
        margin-bottom: 0.3rem;
    }

    .chroma-content p {
        font-size: 0.8rem;
        margin-bottom: 0.7rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        width: 100%;
    }
    
    .product-image {
        height: 250px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 1024px) {
    .nav-container {
        padding: 0 30px;
    }
    
    .search-box input {
        width: 140px;
    }

    .nav-icons {
        gap: 0.5rem;
    }
    
    .hero-content h1 {
        font-size: 3.8rem;
    }

    .hero-content p {
        font-size: 1.3rem;
    }

    .hero-image img {
        height: 550px;
    }

    .hero-stats {
        gap: 2.5rem;
    }
    
    .chroma-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.8rem;
    }

    .chroma-card {
        height: 240px;
    }

    .chroma-content {
        padding: 1.3rem;
    }

    .chroma-icon {
        font-size: 1.7rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
    
    #cartModal .modal-content {
        width: 90%;
        max-width: 600px;
    }
}

/* Large Devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
    .hero-slide {
        padding: 8rem 0;
        gap: 8rem;
    }

    .hero-content h1 {
        font-size: 4.8rem;
    }

    .hero-content p {
        font-size: 1.5rem;
        max-width: 600px;
    }

    .hero-image img {
        height: 650px;
    }

    .hero-stats {
        gap: 4rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }
    
    .chroma-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .chroma-card {
        height: 280px;
    }

    .chroma-content {
        padding: 1.5rem;
    }

    .chroma-icon {
        font-size: 1.8rem;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
        width: 100%;
    }

    .nav-container {
        max-width: 1400px;
        width: 100%;
    }

    .hero-content h1 {
        font-size: 5.5rem;
        line-height: 1.05;
    }

    .hero-content p {
        font-size: 1.6rem;
        max-width: 650px;
    }

    .hero-slide {
        gap: 10rem;
        width: 100%;
    }

    .hero-image img {
        height: 700px;
        max-height: 80vh;
        width: 100%;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        width: 100%;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .product-actions {
        opacity: 1;
    }
    
    .category-card:hover {
        transform: none;
    }
    
    .product-card:hover {
        transform: none;
    }
    
    .product-card:hover .product-image img {
        transform: none;
    }
    
    .cta-btn:hover {
        transform: none;
    }
    
    .btn-primary:hover {
        transform: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image img,
    .category-card img,
    .product-image img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Landscape Orientation for Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 50vh;
        height: auto;
        width: 100%;
    }

    .hero-slide {
        padding: 2rem 0;
        min-height: 40vh;
        width: 100%;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-image img {
        height: 200px;
        max-height: 30vh;
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .modal,
    .overlay,
    .newsletter,
    .footer {
        display: none;
    }
    
    .hero {
        background: none;
        color: black;
    }
    
    .product-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .product-actions {
        display: none;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Enhanced Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #0f0f0f;
        --text-color: #e4e4e7;
        --card-bg: #18181b;
        --border-color: #27272a;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .navbar {
        background: rgba(24, 24, 27, 0.95);
        border: 1px solid rgba(63, 63, 70, 0.3);
    }

    .product-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
    }

    .search-box {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
    }

    .search-box input {
        background: transparent;
        color: var(--text-color);
    }

    .dropdown-menu {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
    }

    .modal-content {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
    }
}

/* Manual Dark Mode Support (when data-theme="dark") */
[data-theme="dark"] {
    /* This uses the CSS variables defined in styles.css */
}

[data-theme="dark"] .product-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    color: var(--text-color);
    transition: all 0.3s ease;
}

[data-theme="dark"] .product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(75, 0, 130, 0.3);
}

[data-theme="dark"] .search-box {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
}

[data-theme="dark"] .search-box input {
    background: transparent;
    color: var(--text-color);
}

[data-theme="dark"] .search-box input::placeholder {
    color: var(--text-light);
}

[data-theme="dark"] .search-btn {
    color: var(--text-color);
}

[data-theme="dark"] .search-btn:hover {
    color: var(--primary-color);
}

[data-theme="dark"] .dropdown-menu {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .footer {
    background: var(--footer-bg);
    color: var(--footer-text);
}

[data-theme="dark"] .footer a {
    color: var(--footer-text);
}

[data-theme="dark"] .footer a:hover {
    color: var(--primary-color);
}

/* Dark mode mobile navigation */
@media (max-width: 768px) {
    [data-theme="dark"] .nav-center,
    body.dark-theme .nav-center {
        background: rgba(24, 24, 27, 0.98) !important;
        border: 1px solid rgba(63, 63, 70, 0.3) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5) !important;
    }

    [data-theme="dark"] .nav-links a,
    body.dark-theme .nav-links a {
        color: var(--text-color) !important;
    }

    [data-theme="dark"] .nav-links a:hover,
    [data-theme="dark"] .nav-links a.active,
    body.dark-theme .nav-links a:hover,
    body.dark-theme .nav-links a.active {
        color: var(--primary-color) !important;
        background: rgba(75, 0, 130, 0.1) !important;
    }
}

@media (max-width: 480px) {
    [data-theme="dark"] .nav-center,
    body.dark-theme .nav-center {
        background: rgba(24, 24, 27, 0.98) !important;
        border: 1px solid rgba(63, 63, 70, 0.3) !important;
    }


}

/* Dark mode form styling */
[data-theme="dark"] input,
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
}

/* Dark mode button styling */
[data-theme="dark"] .btn {
    background: var(--card-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-track {
    background: rgba(75, 0, 130, 0.1);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    border: 3px solid rgba(24, 24, 27, 0.8);
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-color, #4B0082);
    outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color, #4B0082);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Update CSS variables for purple theme */
:root {
    --primary-color: #4B0082;      /* Dark Purple */
    --secondary-color: #D8BFD8;    /* Light Purple (Lavender) */
    --background-color: #FFFFFF;   /* White */
    --text-color: #333;
    --text-light: #666;
    --border-color: #e1e5e9;
    --success-color: #27ae60;
    --warning-color: #ffc107;
    --error-color: #e74c3c;
}

/* Enhanced product card responsiveness */
@media (max-width: 768px) {
    .product-card-actions {
        gap: 0.3rem;
        width: 100%;
    }

    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
        min-width: 80px;
    }


}

@media (max-width: 480px) {
    .product-card-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        min-width: 70px;
        width: 100%;
    }


}
